# Logs
logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*
lerna-debug.log*

# Node modules
node_modules/
backend/node_modules/
frontend/nextdoorbuddy/node_modules/

# Package lock files (optional - remove if you want to commit them)
frontend/nextdoorbuddy/package-lock.json
backend/package-lock.json

# Build output
dist/
dist-ssr/
build/
*.tsbuildinfo

# Environment files
*.env
*.env.local
*.env.development.local
*.env.test.local
*.env.production.local
backend/.env*

# Runtime data
pids
*.pid
*.seed
*.pid.lock

# Coverage directory used by tools like istanbul
coverage/
*.lcov

# nyc test coverage
.nyc_output

# Dependency directories
jspm_packages/

# Optional npm cache directory
.npm

# Optional eslint cache
.eslintcache

# Optional REPL history
.node_repl_history

# Output of 'npm pack'
*.tgz

# Yarn Integrity file
.yarn-integrity

# parcel-bundler cache (https://parceljs.org/)
.cache
.parcel-cache

# Next.js build output
.next

# Nuxt.js build / generate output
.nuxt

# Gatsby files
.cache/
public

# Storybook build outputs
.out
.storybook-out

# Temporary folders
tmp/
temp/

# Editor directories and files
.vscode/*
!.vscode/extensions.json
.idea/
*.swp
*.swo
*~

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Docker
.dockerignore

# Uploads (if you don't want to commit uploaded files)
backend/uploads/*
!backend/uploads/.gitkeep
