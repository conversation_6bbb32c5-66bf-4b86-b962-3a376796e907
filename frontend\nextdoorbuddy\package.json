{"name": "nextdoorbuddy", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc -b && vite build", "preview": "vite preview", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0"}, "dependencies": {"@types/leaflet": "^1.9.18", "@types/leaflet-draw": "^1.0.12", "framer-motion": "^12.18.1", "i18next": "^25.2.1", "i18next-browser-languagedetector": "^8.2.0", "i18next-http-backend": "^3.0.2", "leaflet": "^1.9.4", "leaflet-draw": "^1.0.4", "lucide-react": "^0.517.0", "react": "^19.1.0", "react-dom": "^19.1.0", "react-i18next": "^15.5.3", "react-leaflet": "^5.0.0", "react-router-dom": "^7.5.0", "socket.io-client": "^4.8.1"}, "devDependencies": {"@eslint/js": "^9.21.0", "@tailwindcss/postcss": "^4.1.3", "@types/react": "^19.0.10", "@types/react-dom": "^19.0.4", "@vitejs/plugin-react": "^4.3.4", "autoprefixer": "^10.4.21", "eslint": "^9.21.0", "eslint-plugin-react-hooks": "^5.1.0", "eslint-plugin-react-refresh": "^0.4.19", "globals": "^15.15.0", "postcss": "^8.5.3", "tailwindcss": "^4.1.3", "typescript": "~5.7.2", "typescript-eslint": "^8.24.1", "vite": "^6.2.0"}}