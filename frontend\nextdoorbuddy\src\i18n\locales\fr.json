{"common": {"loading": "Chargement...", "error": "<PERSON><PERSON><PERSON>", "success": "Su<PERSON>ès", "save": "Enregistrer", "cancel": "Annuler", "delete": "<PERSON><PERSON><PERSON><PERSON>", "edit": "Modifier", "create": "<PERSON><PERSON><PERSON>", "back": "Retour", "next": "Suivant", "previous": "Précédent", "search": "<PERSON><PERSON><PERSON>", "filter": "<PERSON><PERSON><PERSON>", "close": "<PERSON><PERSON><PERSON>", "confirm": "Confirmer", "yes": "O<PERSON>", "no": "Non"}, "navigation": {"home": "Accueil", "events": "Événements", "trocs": "Trocs", "myEvents": "Mes événements", "chat": "Cha<PERSON>", "profile": "Mon Profil", "adminDashboard": "Tableau de bord", "manageUsers": "<PERSON><PERSON><PERSON> les utilisateurs", "manageQuartiers": "<PERSON><PERSON><PERSON> les quartiers", "logout": "Déconnexion"}, "auth": {"login": {"title": "Bon retour !", "subtitle": "Connectez-vous pour retrouver vos voisins", "email": "Adresse e-mail", "password": "Mot de passe", "showPassword": "Afficher le mot de passe", "hidePassword": "Masquer le mot de passe", "loginButton": "Se connecter", "loggingIn": "Connexion en cours...", "noAccount": "Pas encore de compte ?", "signUp": "Inscrivez-vous", "forgotPassword": "Mot de passe oublié ?", "errors": {"invalidCredentials": "Erreur lors de la connexion", "emailRequired": "L'adresse e-mail est requise", "passwordRequired": "Le mot de passe est requis"}}, "signup": {"title": "Inscription", "firstName": "Prénom", "lastName": "Nom", "email": "Adresse e-mail", "password": "Mot de passe", "confirmPassword": "Confirmer le mot de passe", "phone": "Téléphone", "address": "<PERSON><PERSON><PERSON>", "birthDate": "Date de naissance", "quartier": "Quartier", "signupButton": "S'inscrire", "signingUp": "Inscription en cours...", "hasAccount": "Déjà un compte ?", "login": "Connectez-vous", "errors": {"passwordMismatch": "Les mots de passe ne correspondent pas", "emailExists": "Cette adresse e-mail est déjà utilisée", "registrationError": "E<PERSON>ur lors de l'inscription"}}}, "home": {"welcome": "Bienvenue, {{name}} !", "subtitle": "Découvrez ce qui se passe dans votre quartier et restez connecté avec vos voisins", "todaysEvents": "Événements d'aujourd'hui", "myEvents": "Mes événements", "availableTrocs": "Trocs disponibles", "noEventsToday": "Aucun événement aujourd'hui", "noUserEvents": "Vous ne participez à aucun événement", "noTrocs": "Aucun troc disponible", "viewAll": "Voir tout", "participate": "Participer", "viewDetails": "Voir les détails", "adminSection": "Administration", "greeting": "Bonjour"}, "events": {"title": "Événements", "createEvent": "C<PERSON>er un événement", "editEvent": "Modifier l'événement", "createNewEvent": "Créer un nouvel événement", "eventName": "Nom de l'événement", "description": "Description", "eventDate": "Date de l'événement", "address": "<PERSON><PERSON><PERSON>", "eventType": "Type d'événement", "photo": "Photo", "url": "URL", "noEvents": "Aucun événement trouvé", "loadingEvents": "Chargement des événements...", "loadingEvent": "Chargement de l'événement...", "participants": "Participants", "participate": "Participer", "cancelParticipation": "Annuler la participation", "alreadyParticipating": "Vous participez d<PERSON>", "eventPassed": "Événement passé", "organizer": "Organisateur", "location": "<PERSON><PERSON>", "date": "Date", "time": "<PERSON><PERSON>", "website": "Site web", "errors": {"nameRequired": "Le nom de l'événement est obligatoire", "dateRequired": "La date de l'événement est obligatoire", "addressRequired": "L'adresse de l'événement est obligatoire", "invalidUrl": "Veuillez entrer une URL valide (ex: https://example.com)", "createError": "Erreur lors de la création de l'événement", "updateError": "Erreur lors de la mise à jour de l'événement", "deleteError": "Erreur lors de la suppression de l'événement", "participationError": "<PERSON><PERSON><PERSON> lo<PERSON> de <PERSON>", "loadError": "<PERSON><PERSON><PERSON> lors du chargement"}, "success": {"created": "Événement créé avec succès", "updated": "Événement mis à jour avec succès", "deleted": "Événement supprimé avec succès", "participated": "Participation confirmée", "participationCancelled": "Participation annulée"}}, "trocs": {"title": "Trocs", "createTroc": "<PERSON><PERSON><PERSON> un troc", "editTroc": "Modifier le troc", "myTrocs": "<PERSON><PERSON> trocs", "trocTitle": "Titre", "description": "Description", "proposedItem": "Objet proposé", "searchedItem": "Objet recherché", "category": "<PERSON><PERSON><PERSON><PERSON>", "price": "Prix", "maxBudget": "Budget maximum", "images": "Images", "type": {"offer": "Je propose", "request": "Je recherche"}, "noTrocs": "<PERSON><PERSON>n troc trouvé", "loadingTrocs": "Chargement des trocs...", "errors": {"titleRequired": "Le titre est obligatoire", "descriptionRequired": "La description est obligatoire", "itemRequired": "L'objet est obligatoire", "maxImages": "Vous ne pouvez pas ajouter plus de {{max}} images au total", "createError": "<PERSON><PERSON>ur lors de la création du troc", "updateError": "Erreur lors de la mise à jour du troc", "deleteError": "<PERSON><PERSON><PERSON> lors de la suppression du troc"}, "success": {"created": "<PERSON><PERSON><PERSON> c<PERSON><PERSON> avec succès", "updated": "Troc mis à jour avec succès", "deleted": "Troc supprimé avec succès"}}, "chat": {"title": "Cha<PERSON>", "noRoom": "Sélectionnez une conversation", "typeMessage": "Tapez votre message...", "send": "Envoyer", "typing": "{{user}} est en train d'écrire...", "multipleTyping": "Plusieurs personnes écrivent...", "online": "En ligne", "offline": "<PERSON><PERSON> ligne", "lastSeen": "Vu pour la dernière fois", "noMessages": "Aucun message", "loadingMessages": "Chargement des messages...", "errors": {"sendError": "Erreur lors de l'envoi du message", "loadError": "Erreur lors du chargement des messages"}}, "profile": {"title": "Mon Profil", "editProfile": "Modifier le profil", "personalInfo": "Informations personnelles", "firstName": "Prénom", "lastName": "Nom", "email": "Adresse e-mail", "phone": "Téléphone", "address": "<PERSON><PERSON><PERSON>", "birthDate": "Date de naissance", "quartiers": "Quartiers", "addQuartier": "Ajouter un quartier", "principalQuartier": "Quartier principal", "changePassword": "Changer le mot de passe", "currentPassword": "Mot de passe actuel", "newPassword": "Nouveau mot de passe", "confirmPassword": "Confirmer le mot de passe", "updateProfile": "Mettre à jour le profil", "errors": {"updateError": "E<PERSON>ur lors de la mise à jour du profil", "passwordMismatch": "Les mots de passe ne correspondent pas", "invalidEmail": "Adresse e-mail invalide", "phoneInvalid": "Numéro de téléphone invalide"}, "success": {"updated": "Profil mis à jour avec succès", "passwordChanged": "Mot de passe modifié avec succès"}}, "admin": {"dashboard": "Tableau de bord administrateur", "users": "Utilisateurs", "quartiers": "Quartiers", "trocs": "Trocs", "events": "Événements", "statistics": "Statistiques", "manageUsers": "<PERSON><PERSON><PERSON> les utilisateurs", "manageQuartiers": "<PERSON><PERSON><PERSON> les quartiers", "manageTrocs": "<PERSON><PERSON><PERSON> les trocs", "manageEvents": "<PERSON><PERSON>rer les événements"}, "validation": {"required": "Ce champ est obligatoire", "email": "Adresse e-mail invalide", "password": "Le mot de passe est requis", "url": "URL invalide", "date": "Date invalide", "phone": "Numéro de téléphone invalide", "minLength": "Minimum {{min}} caractères", "maxLength": "Maximum {{max}} caractères"}}