user  nginx;
worker_processes auto;

events { worker_connections 1024; }

http {
  include       mime.types;
  default_type  application/octet-stream;
  sendfile       on;
  keepalive_timeout 65;

  map $http_upgrade $connection_upgrade {
    default   upgrade;
    ''        close;
  }

  # Définition des upstreams pour nommage
  upstream frontend {
    server frontend:80;     
  }
  upstream backend {
    server backend:3000;     
  }

  # Configuration pour développement local (HTTP seulement)
  server {
    listen      80;
    server_name localhost;

    # SPA et ressources statiques
    location / {
      proxy_pass         http://frontend;
      proxy_http_version 1.1;
      proxy_set_header   Host              $host;
      proxy_set_header   X-Real-IP         $remote_addr;
      proxy_set_header   X-Forwarded-For   $proxy_add_x_forwarded_for;
      proxy_set_header   X-Forwarded-Proto $scheme;
    }

    # API Node.js
    location /api/ {
      proxy_pass         http://backend;
      proxy_http_version 1.1;
      proxy_set_header   Host              $host;
      proxy_set_header   X-Real-IP         $remote_addr;
      proxy_set_header   X-Forwarded-For   $proxy_add_x_forwarded_for;
      proxy_set_header   X-Forwarded-Proto $scheme;
    }

    # WebSocket (socket.io…)
    location /socket.io/ {
      proxy_pass         http://backend;
      proxy_http_version 1.1;
      proxy_set_header   Upgrade $http_upgrade;
      proxy_set_header   Connection $connection_upgrade;
      proxy_set_header   Host $host;
      proxy_set_header   X-Real-IP           $remote_addr;
      proxy_set_header   X-Forwarded-For     $proxy_add_x_forwarded_for;
      proxy_set_header   X-Forwarded-Proto   $scheme;
    }
  }
}
