-- Utilisateurs (mot de passe: 'Admin123!' pour l'admin et 'User123!' pour l'utilisateur)
-- Format du mot de passe hashé avec crypto: salt:hash
INSERT INTO "Utilisateur" (nom, prenom, email, password, adresse, date_naissance, telephone, quartier_id, role)
VALUES
  ('<PERSON><PERSON>', '<PERSON>', '<EMAIL>', 'e4bb35f038b4ffdad9a55bbad57f243a:79acdc661e52c1280c3ed0b66a39a71b6905f340bea136c4c670b31611180d186da93e9c55a73391640f963b3a6829ad6f2711aa3ac143aef13921520858f694', '10 rue de Rivoli', '1990-04-15', '0601020304', 1, 'user'),
  ('<PERSON>', '<PERSON>', '<EMAIL>', 'e4bb35f038b4ffdad9a55bbad57f243a:1f11c9cb0cdd54e453f83721eb8dc60a03aa0bc42ec3b19940a5fd04a66182360d8a9501739208f9ebcb0bc3d64ad78dd709c841b7e71a1f1881bb65db8a2e34', '3 avenue Junot', '1985-06-20', '0605060708', 2, 'user'),
  ('Verrecchia', 'Lucas', '<EMAIL>', 'e4bb35f038b4ffdad9a55bbad57f243a:ff4d689138b7baf27715c456a21ed5e580b5f0177ebb84e23814f80dd7c868fc22fadc5884dd87144c2c37f6deb43fda95f854a227a9a209ee2aa75288d90a64', '37 avenue du val de beauté', '1995-03-27', '0629463796', 1, 'admin'),
  ('Se<PERSON>', '<PERSON>', '<EMAIL>', '8a3a0fbfdc53e9c61b6f6bf1c4d4bc4c:299cf0542d0e145e909dde9db3853b8eb32edba5d15b3784d84c805b2f557da012d538e2d28e32ad34832c390c01b4ad80a9764b72bc924bb0634d17d2ead3c6', '35 Rue Marcadet 75018 Paris', '2001-03-20', '0627940990', 79, 'user');


-- Événements
INSERT INTO "Evenement" (organisateur_id, nom, description, date_evenement, lieu, type_evenement)
VALUES
  (1, 'Fête de voisins', 'Rencontre entre voisins du quartier', '2025-06-15 18:00:00', 'Place du marché', 'fête'),
  (2, 'Atelier compost', 'Atelier découverte du compost', '2025-06-20 14:00:00', 'Jardin partagé', 'atelier');

-- Participations
INSERT INTO "Participation" (utilisateur_id, evenement_id, date_inscription)
VALUES
  (1, 2, NOW()),
  (2, 1, NOW());

-- Relations
INSERT INTO "Relation" (utilisateur1_id, utilisateur2_id, type_relation, date_debut)
VALUES
  (1, 2, 'voisin', '2024-10-01'),
  (2, 1, 'voisin', '2024-10-01');

INSERT INTO public."Quartier" (id, nom_quartier, ville, geom, code_postal, description) VALUES (1, 'Arts-et-Métiers', 'Paris', '0106000020E61000000100000001030000000100000038000000F9CA0EC5B5E10240465AC58DBE6E484086084F9ECAE0024047A7A047B06E484041F713CC94E0024010F92255AE6E4840D80755BE54E0024051F63708AC6E4840B5AA2924EBDF0240D689329DA56E484087B1C74CEDDE0240E9B2DC9B976E48405E7D1EA602DE024045B414CB886E4840B207DD49BADD0240125127C6826E484036E6EA00ADDD0240272D2116836E4840D5A1B54908DC0240FE0B21038B6E484014C8593C93DA02400AED434F926E48400CAF93AB74D802404258D6849B6E48404A68493954D702400FD75EB4A06E4840D002A0127FD3024077116B85B26E48400D723AECE8CF02404C4E3981A56E484092CC97F4D7D002401867752CC06E48405C2C9E78FBD102409E11CCB4E06E4840317D87CBE4D20240895613E8FA6E4840E935399286D50240F4A50435466F4840587C788AC8D80240C52D5B3E3A6F484033E3699D56D902407618C434386F4840DC87EBD1DBD902404AB83190366F48404D27D8D6B3DB0240C55B09F3316F4840EF21F5B741DF024015F8CD8B296F484028CB391996E00240B41A6979266F4840C0C72FD815E60240AEAB44C1196F4840B9DDA29E9FE60240E8157991186F484023AE947716E70240A03EF37F176F48409790948F52E70240E840BEF5166F484072CD8E759FE70240B2012344166F48402DC094B1D0E7024041F1CDD2156F4840B2C1E56CF7E702400481A979156F4840F9B19A5C1FE802405802DCE2136F48407B98890349E802409486AA56126F48409CCC642DA0E80240C68DCC170F6F4840C1673555D9E80240CDEFAEF70C6F4840E6F2C438F5E80240F414EFED0B6F4840937317D7F5E8024067BC08E80B6F4840767D367CFBE802403ACE73B20B6F48402C8CDB9FFEE80240F2B03B940B6F484053A66FB41EE90240C8499B620A6F4840BF3D781BF9E80240FF21A173096F48406AC2B405EDE80240BC1CAF26096F4840D8147A71E6E8024029A99FFC086F48408FEFB24EE6E80240A076C2FB086F4840EA8CB2A4CEE8024030C9B665086F48405AAB1D29B6E702407311196F016F4840178F938697E60240D74D5551FA6E48403FEB881924E5024000CC9E18F16E484091B9005308E5024086A6C041F06E48401DC810FDEFE4024058BF0F6AEF6E48402C43FB48D5E40240746F535AEE6E4840D61F8642BEE40240721F094CED6E484062740D12ADE4024001F95B67EC6E4840F48535BA25E202406851AB59C56E4840F9CA0EC5B5E10240465AC58DBE6E4840', null, null);
INSERT INTO public."Quartier" (id, nom_quartier, ville, geom, code_postal, description) VALUES (2, 'Mail', 'Paris', '0106000020E6100000010000000103000000010000001B0000009539C04B53C60240C363F75DB56E48409D0DA9C7FDC50240167C5AADA56E4840F3D4E6A7C4C20240872C5D11B26E4840D927BB4DECBA0240806F202AD06E4840885E7CF9EABA02400EE23C2FD06E48409C80356EC9BA0240D8515EB0D06E4840786484ACBBBA0240DB8E4EE2D06E4840AE93B550D8BA0240FA0C849CD26E48401F843FDF5EBB0240C37FA2D9DA6E484026CDA898F5BA02402523CD5EE26E48401D59056869BA024099002F94E56E4840EC99E6CF6FBB0240EE8B22270D6F48406C766D872EBC0240B92A71CB296F48406D14BE643CBC024055FAEA232F6F484003981BF39FBD024027EBCE1D606F48403494E5EC9ABE0240BEDC8BF28C6F48401382217575C80240782BC93B736F4840E3F955FB78C802406B253700656F4840F7FBBEB972C80240A9F0BF605A6F4840C086B4EA2EC8024082F50AE6346F48402847A1841CC8024056C744952C6F48405C4BBF0C68C70240C5A87E38156F4840F8921CB363C7024089C379EC0B6F48408120AFCC4FC702404EDD6AE9026F4840AA27FBE6FBC60240D96A2187E46E48408FC2FEE2A8C602401D614BC6CA6E48409539C04B53C60240C363F75DB56E4840', null, null);
INSERT INTO public."Quartier" (id, nom_quartier, ville, geom, code_postal, description) VALUES (3, 'Combat', 'Paris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null, null);
INSERT INTO public."Quartier" (id, nom_quartier, ville, geom, code_postal, description) VALUES (4, 'Gros-Caillou', 'Paris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null, null);
INSERT INTO public."Quartier" (id, nom_quartier, ville, geom, code_postal, description) VALUES (5, 'Plaisance', 'Paris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null, null);
INSERT INTO public."Quartier" (id, nom_quartier, ville, geom, code_postal, description) VALUES (6, 'Enfants-Rouges', 'Paris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null, null);
INSERT INTO public."Quartier" (id, nom_quartier, ville, geom, code_postal, description) VALUES (7, 'Saint-Merri', 'Paris', '0106000020E61000000100000001030000000100000025000000F01120432CD20240BDC6C09E6D6D4840194F8D38E7D102409A2C05BC656D4840DF0E3A109CCC0240B4401DB5896D48405245135916CA0240BFB51A43966D4840175CB07640C60240D4774C9AA86D48402C6111BDA0C602409011D87AB26D4840FF5F8A8BC3C602408D1825C4B46D4840CC02603508C7024053456E88B76D4840E5DD56A508C702406454F18CB76D4840200168BE44C70240FCB50C3AB96D4840821ACEB0B7C70240A580A466BC6D48409ED6EBDBB7C70240A88DD567BC6D48406B4E1517D0C702402FC2999EBD6D4840DCF5A52821C90240F6E85C29E36D48408A74F07B27C9024085A3B7DDE36D4840C9C18F4C80CB0240A814F857276E4840F5F85685AECB02400B80B47D2C6E484063BC5E9C1ECD0240E5E832BA556E4840665BF02C2FD0024041718E904A6E48405708F755A4D302403CF5B22F3E6E4840F2CA5292E9D30240ABFE1D613C6E484087781DA1DADA02408F49491A176E4840A408538196D70240D2677A74E36D48401D3D408A5CD502404F4A93EDC06D4840DE5F4539E3D4024046CA41EFB26D48401979C5F060D40240B9600F04AE6D4840AD54512F56D30240E2134DA58F6D4840411EF03B98D202405C9F2CF4796D484035C6D9538DD20240243B4EB5786D484083EAD60E78D20240E7017D47766D48406C68D62F69D202400114F894746D484065974DE562D20240F36568DB736D4840C3AA808652D2024054B3BAFE716D48402CCC4ADE4DD20240B15B1576716D48407871708446D20240C07E659E706D4840CAAEA9352DD20240AA6C74BA6D6D4840F01120432CD20240BDC6C09E6D6D4840', null, null);
INSERT INTO public."Quartier" (id, nom_quartier, ville, geom, code_postal, description) VALUES (8, 'Saint-Victor', 'Paris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null, null);
INSERT INTO public."Quartier" (id, nom_quartier, ville, geom, code_postal, description) VALUES (9, 'Villette', 'Paris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null, null);
INSERT INTO public."Quartier" (id, nom_quartier, ville, geom, code_postal, description) VALUES (10, 'Place-Vendôme', 'Paris', '0106000020E61000000100000001030000000100000022000000DFEC9CC3D2A702408C99E076B56E4840390310BE9CA602406FC6A73E976E484099F359F7389E024041474A45C26E4840B74F980AC8980240EC51D48BDE6E4840A2ADE9ABAD960240BD79905CE96E48401BF0210599960240C8F592CBE96E48402B45D238C0980240CE1FD96C186F4840D36C63DAC098024056FBEF7A186F484031D2E4A8EE9802406AE470791C6F48402BA793F0A999024034D583CB2D6F48405A89A827419A024028B9A8C73B6F4840CA2EDC0DED990240EEEFB5194A6F4840802D394A0A9A02405B8FA7674A6F484090BC268B1F9A0240CD6F44A04A6F4840A465B23A8E9A024006B039C74B6F48407A11945AA39A02402E1488FF4B6F4840EDA4B5D1019B02406F3344FB4C6F48403E77C609509B0240F1F366D04D6F4840B2DCBFAC529B02407F9994D74D6F4840C5A523B8B99F0240D65D19D5596F48404C2CDA14C29F0240B5D9CE76596F4840CCB8494EC29F0240D4903D74596F4840F550A1FFC4A302400A4AEA782B6F484026009CE377A402402B31B44B266F4840727C945B2FA50240944ADFEB206F4840AC5E4AC45FA70240A7837025196F4840BB27ABF75FA70240F1C3B924196F4840AABB83F75DAB02407ED5B4C40A6F48408FCFB30B5CAB02404EC814970A6F484016F1D79979AA0240F4D4EC12F46E48401549574410AA02407725BFEAEB6E48400894DEECD6A802401C17FEBACE6E48407933F04BD6A802408FA374ABCE6E4840DFEC9CC3D2A702408C99E076B56E4840', null, null);
INSERT INTO public."Quartier" (id, nom_quartier, ville, geom, code_postal, description) VALUES (11, 'Pont-de-Flandre', 'Paris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null, null);
INSERT INTO public."Quartier" (id, nom_quartier, ville, geom, code_postal, description) VALUES (12, 'Faubourg-du-Roule', 'Paris', '0106000020E6100000010000000103000000010000002A0000000F8D0554E97E0240FC3767D2596F484049507F6B1D7B0240C29E33A13A6F48403289E350A3730240DA119A02616F4840D7E09557307102404F7D4F696D6F48400A4BCB5B686B0240993D34E68A6F4840C9F43A1889680240AF02B981996F48401B9296993E5D0240EB9208DAD26F48407A874B5A815C0240199014A6D66F48408932E98F7B5C02404067B3C2D66F4840E6B66787575C0240F34B0673D76F48405EA20EB83D5C024013E1A8FAD76F4840D67A49BB3D5C02405181F4FAD76F4840F5583378805C02401793DCC3DD6F48406E83FA5EB85C0240E0D15A96E26F484044200CDDC75C02405BECA5EAE36F4840BA4F18433C5E02400C4C9B04047048406D008D1241620240EBF7A5AA5C7048409CA7F0F355620240D920427E5E7048400D201B1C9F620240E6BA99E464704840F817A35A4163024006A9571267704840E6F93EFB83630240267560F767704840513D73BACC630240207473F168704840C1A92EB7CD630240B20ED9F468704840BCDA09E5D0630240FBA0C4FF687048409CEBCB576268024088A295B3787048400F729874BB6C0240B95DA3AF8770484058C9A186BD6D02403268687A7F7048406C16E42F20710240C27155AF637048402D420CE41B7402403907761B4D704840AAEA1A39667602400DA7BD7F2F7048409E31DCCCE878024013225DEE05704840282604A94979024058851B18FD6F4840B5F29E51D6790240651393DAF36F48405F0D73833A7A024049B62AB4EE6F48403A606BEE157C0240C79F38DFDB6F48409CD3EFBC457D0240F7435FADCB6F4840CE8A78A6FF7D02408D3CB70EC16F4840C7AF87C24A7C02402E700A3BB96F4840B7B4B2D61A8002404546D08BA46F4840B85F58C6EA7E0240F81B697F8B6F4840FD47D6E4D480024086611C9F696F48400F8D0554E97E0240FC3767D2596F4840', null, null);
INSERT INTO public."Quartier" (id, nom_quartier, ville, geom, code_postal, description) VALUES (24, 'Sainte-Avoie', 'Paris', '0106000020E61000000100000001030000000100000016000000D782323BA1DD0240EE53479E3C6E4840F6442622F0DA024056EB06A3166E484087781DA1DADA02408F49491A176E4840F2CA5292E9D30240ABFE1D613C6E48405708F755A4D302403CF5B22F3E6E4840665BF02C2FD0024041718E904A6E484063BC5E9C1ECD0240E5E832BA556E484044ECE5553DCD0240E8820221596E4840C011AFA2BDCE0240C1E6DFFF836E4840C36B8B67BECE02403939E315846E48400D723AECE8CF02404C4E3981A56E4840D002A0127FD3024077116B85B26E48404A68493954D702400FD75EB4A06E48400CAF93AB74D802404258D6849B6E484014C8593C93DA02400AED434F926E4840D5A1B54908DC0240FE0B21038B6E484036E6EA00ADDD0240272D2116836E4840B207DD49BADD0240125127C6826E4840056EA3A6F4DD0240306DE6A5816E48400E9A25E0BCDE0240A73E65067C6E48403921A24CE5E00240156EB6BA696E4840D782323BA1DD0240EE53479E3C6E4840', null, null);
INSERT INTO public."Quartier" (id, nom_quartier, ville, geom, code_postal, description) VALUES (13, 'Europe', 'Paris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null, null);
INSERT INTO public."Quartier" (id, nom_quartier, ville, geom, code_postal, description) VALUES (14, 'Saint-Georges', 'Paris', '0106000020E6100000010000000103000000010000003B000000E2704A75BDB6024002002FBE3A704840DBF786688BB402405350A4FE3C7048400203591161B2024094CCC12B38704840B294327400AF024013F145FB3B704840EB2B892CBAAC024027A5ACB53E70484081ACB9E796A90240661AAA643C70484047FB2BD4D5A8024020FA809738704840E43E6EA3F3A50240DF9CE702267048405F8B42AA35A4024097344E98207048400498E3B1649D0240131422CB12704840C1F294E5619D0240BEA2748114704840245CDFC4959D02404F3D0D425D704840AE64F90D979D024056F8DB2D5F704840F10838B3989D0240E3E4CA9761704840935B4C1DBD9D02402136DA74977048400A52DC81BD9D0240B233C32A987048403C4977CAD29D024059FE7AA4BE704840BD271F7DF19D02407B19D38FE970484009533536119E024039F613E915714840B49A24EB4C9E0240A766BE851671484077D3493E939E02406C02423E17714840B7F54E2FD79E0240C1C4692518714840DE8AAB6D339F0240AC343D5F19714840DE358AD8689F0240C26FF8141A7148403CC2F21D08A00240E258BE591D714840FE003A9D77A00240BE7C6BDF217148403DD2E4B7CEA00240C2EFD1672571484053B25F260FA202404ACDCB6632714840D7AB209935A202406D36FEF533714840349675A053A20240D280C62D35714840E52F5CEDB2A202407C143C0B397148401C9D2BF43DA30240AEE33B013771484073C2BD10CEA40240D908EB2231714840C76AAEF8F9A40240B5C80D7E30714840481AD3C52FA80240237D3370247148409BFA2F44D7A80240CF5C3FFB2171484085051E63E7A80240B546D1A8217148406976C1EFF5A8024031E26A5E217148405AB30D822AA90240831A1A4C20714840F55D526157A9024012BC04621F71484021A4FACAAAA902408DFD5AD31D714840EEC259A2D9AD02408E2743D509714840D0F825FD06AE024040EB7BFC08714840A869FFDB14B20240DB8C8B9BF5704840FD681A2247B202403CEF38ABF4704840CD0D1D6E7BB202402F416FC0F37048408B01347095B202401542AD4BF3704840B13DFAEECBB20240AADD0257F2704840A70C5964CCB202408B6DF254F27048400B24590BDDB30240A9403676EF704840ABE6F0B1FEB30240DAEDA429EF704840114B1E1C53B4024093119069EE704840413B396F4BB702407BA663A7E77048403D6E3B1779B702406F1C7E3FE77048404DC915897DB70240A05E0FAFE670484009B8BDD285B702404387C5A1E57048406E39709A2BB90240155D0D59B0704840D965871809B8024055CED0A584704840E2704A75BDB6024002002FBE3A704840', null, null);
INSERT INTO public."Quartier" (id, nom_quartier, ville, geom, code_postal, description) VALUES (15, 'Faubourg-Montmartre', 'Paris', '0106000020E61000000100000001030000000100000018000000754330C5D7B80240C9D3E979347048409E644961FFBC02401A14558F3170484034281046BCBE024025AC18F9327048403840806C47C1024013544C8332704840C8449F9C4CC1024071FBC7F032704840ABDF6EEF0ECA0240C4228D8B3570484094F38E965CC9024021E90F6A21704840B79DED7635C90240BD4D57021A704840E418A50F86C802408678F803E06F48409C4FE80A80C802401AAB04EA9D6F48401382217575C80240782BC93B736F48403494E5EC9ABE0240BEDC8BF28C6F484086EC391C69B802404D1D56119D6F4840452EE07D1CB80240CA77D63E9C6F4840908CD546C7B702403063B6549B6F4840363926925FB20240AE63BB7A8C6F4840E30CB62F0BB3024026270F91A96F4840011D615A68B40240E085D687E46F4840F840507E21B5024053B8697303704840D7C7731FCBB50240825B889D1F7048407BD5FC8360B60240C7F4300E1F704840E8E78CEAE8B602401FDA4A9A367048409EBC6167A9B702403201573E36704840754330C5D7B80240C9D3E97934704840', null, null);
INSERT INTO public."Quartier" (id, nom_quartier, ville, geom, code_postal, description) VALUES (16, 'Notre-Dame', 'Paris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null, null);
INSERT INTO public."Quartier" (id, nom_quartier, ville, geom, code_postal, description) VALUES (21, 'Grandes-Carrières', 'Paris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null, null);
INSERT INTO public."Quartier" (id, nom_quartier, ville, geom, code_postal, description) VALUES (22, 'Necker', 'Paris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null, null);
INSERT INTO public."Quartier" (id, nom_quartier, ville, geom, code_postal, description) VALUES (17, 'Auteuil', 'Paris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null, null);
INSERT INTO public."Quartier" (id, nom_quartier, ville, geom, code_postal, description) VALUES (18, 'Halles', 'Paris', '0106000020E61000000100000001030000000100000020000000C9C18F4C80CB0240A814F857276E48408A74F07B27C9024085A3B7DDE36D48402464B0F40AC9024075762E71E46D484034A8F4131FC20240B53F1F37086E484011484D8273C0024064B1ABE3106E4840124A739F72C002406E913BE8106E4840DF57663503BA02404452B439326E48400CF2575C86B602401E05DAE8446E48407A0E9EE139B70240001D2D735A6E4840E14E833FA6B6024035008C2B5D6E4840A7D4FC5D74B7024023C91EED7B6E4840A398F6C5D8B70240A259BD72886E484009122AC211B80240ACE6598B8F6E4840F2BE29A3DDB80240C2BB7A0DA76E48408551957999BA0240A38DC1E3CD6E4840DB7F627AA5BA0240C7A35FF1CE6E4840654B16C4A5BA02403A59DBF7CE6E4840435C0746B2BA024019469422D06E4840761E1B83BBBA02409BEBFEDED06E4840786484ACBBBA0240DB8E4EE2D06E48409C80356EC9BA0240D8515EB0D06E4840885E7CF9EABA02400EE23C2FD06E4840D927BB4DECBA0240806F202AD06E4840F3D4E6A7C4C20240872C5D11B26E48409D0DA9C7FDC50240167C5AADA56E484041347815FEC5024007332EACA56E4840C36B8B67BECE02403939E315846E4840C011AFA2BDCE0240C1E6DFFF836E484044ECE5553DCD0240E8820221596E484063BC5E9C1ECD0240E5E832BA556E4840F5F85685AECB02400B80B47D2C6E4840C9C18F4C80CB0240A814F857276E4840', null, null);
INSERT INTO public."Quartier" (id, nom_quartier, ville, geom, code_postal, description) VALUES (19, 'Folie-Méricourt', 'Paris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null, null);
INSERT INTO public."Quartier" (id, nom_quartier, ville, geom, code_postal, description) VALUES (20, 'Saint-Gervais', 'Paris', '0106000020E6100000010000000103000000010000003600000068522B6AFDE802409FBF31CD866D48406E155CC74EE70240CC5D095B626D484052CC6944DFE602409EB207F7646D4840339BC1C01BE60240B0D0260B586D48405DFA2C537CE502400F0F6D894B6D484056F2CFF41DE502400A72B26F446D484076AC72F6D0E4024068A95D213F6D48400C0DCAFA6FE40240FE3EE8AD3A6D484042D6EE0148E30240FF0AC52E296D4840FC41C9712DE20240124355C31A6D4840F152F3B8D8E10240DCED44981C6D4840E0BB3BB5BEE102400B1E17B21A6D4840EA23F244ADE10240F3640267196D4840A0512130A4E10240C6187EBC186D484013BF5478A2E10240C6C0589B186D4840B387B7748AE102407F1D44D5166D48401EB69D6083E1024042D20E4F166D484002A6D9907FE10240AE2C84FF156D48408AE5B4B2FEE102402FE9092D136D4840DE700D02EBE1024003D064B4116D48401B958526E5E102404285F240116D48403D8614B4D4E10240F36EAAFC0F6D4840048E43D45EE10240FEF11EEE066D48406C6ACCD4A9E00240430EC9ED0B6D4840E7BB9F32AFDE02404E6F7045186D4840E89024AFD6DB024086FC44A6276D4840C7D5FFC1A3D702402E1C29913E6D4840194F8D38E7D102409A2C05BC656D4840F01120432CD20240BDC6C09E6D6D4840CAAEA9352DD20240AA6C74BA6D6D48407871708446D20240C07E659E706D48402CCC4ADE4DD20240B15B1576716D4840C3AA808652D2024054B3BAFE716D484065974DE562D20240F36568DB736D48406C68D62F69D202400114F894746D484083EAD60E78D20240E7017D47766D484035C6D9538DD20240243B4EB5786D4840411EF03B98D202405C9F2CF4796D4840AD54512F56D30240E2134DA58F6D48401979C5F060D40240B9600F04AE6D4840DE5F4539E3D4024046CA41EFB26D48401D3D408A5CD502404F4A93EDC06D4840A408538196D70240D2677A74E36D484087781DA1DADA02408F49491A176E4840F6442622F0DA024056EB06A3166E4840BEF8FD1EE9DD02407FB78D6DF46D4840F674DEB3D6DE02402AD00EBFE96D4840C53A51DDE1DF02403E63359FDE6D48405F5B6D5CFEDF02400A43028BDD6D4840F3165A29B4E202408BAC09D6C86D48403938E4DFDDE40240ECB6C269BA6D4840F3C92A2F14E70240C12BE107AF6D4840910BA87118EA024087BCE952A06D484068522B6AFDE802409FBF31CD866D4840', null, null);
INSERT INTO public."Quartier" (id, nom_quartier, ville, geom, code_postal, description) VALUES (23, 'Saint-Thomas-d''Aquin', 'Paris', '0106000020E610000001000000010300000001000000330000009970A4BBBA93024098D41D37B46C4840944CA7E177900240024B5006996C48407888E5DD77900240A4A0C906996C4840403601727B8E0240C5313400DE6C4840A2BC27CE618D02406683325D046D48400C3388D1558D0240ABF512F6056D4840E5FBEA04868D0240FFF0C510276D484042B76E0EBC8D0240906F15CC2F6D4840B990D304428F024023A02CF96E6D484021F45FB8A79102404AB74EF5A06D484099A89CBE14950240CB50581CD86D484088C414D4C495024024BCB59AE36D4840227BB0C72497024024D744AAFA6D48403F6BB92ED59802409326867F166E4840DF685E389B9A0240F5C444C1316E4840EAF07A3AAC9A02403CEFBAC5326E48408049D0B3C89A0240A84BC379346E484033E55CE4F69A024064F3173D376E484074E1B97FDB9B02405193D9E9446E48401EDB1789DB9B02403911ACE9446E484019FC88A3D59D02403BB0F9063A6E484004C8FACB61A0024049346FA22C6E4840C9FE54FF69A002409C4475702C6E48401600A64CDDA202403C0194431D6E4840500E113A76A30240E535C1E1196E48405AF758988BA3024077374A68196E4840BAE4CFF7A4A302404494B5D8186E4840F91C3452A7A302402B3FABCA186E4840108A53C5CCA902409312FD2AFF6D48408774A357D9A80240B65A549BE36D48401B63A35B87AA024052FFF79CDB6D48400B0331377EAA024061B139E9DA6D4840BA62DC4A7BAA0240439BC1AFDA6D4840077D7CDC78A90240451C4AD7C66D484079C19E1A44A7024024D68CB49B6D4840CC1D1CB52FA602401E758D10856D48405B014C0A90A502401AB51127776D4840A73A565346A50240F761DC736F6D48409223071BC7A402406A8D0AF45E6D4840313DF9E4B9A40240C8E5F1895D6D4840FCB56C2A91A20240050D856F266D4840D4C3BC3C6DA2024034485C74226D484087E57EE999A00240EC904B98086D48401146E06F179E02400C01180F026D48407FBD5EC76B9D0240165354F3FA6C48402A7EC9C4F49C024026054623F66C484006670F0B299A02401FC8D2A0E36C484004F30D90D697024047E66758D46C48409735374017960240D5743F17C76C48407058DC994B95024083D21DD6C06C48409970A4BBBA93024098D41D37B46C4840', null, null);
INSERT INTO public."Quartier" (id, nom_quartier, ville, geom, code_postal, description) VALUES (25, 'Père-Lachaise', 'Paris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null, null);
INSERT INTO public."Quartier" (id, nom_quartier, ville, geom, code_postal, description) VALUES (26, 'Notre-Dame-des-Champs', 'Paris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null, null);
INSERT INTO public."Quartier" (id, nom_quartier, ville, geom, code_postal, description) VALUES (27, 'Invalides', 'Paris', '0106000020E610000001000000010300000001000000300000000C3388D1558D0240ABF512F6056D4840A2BC27CE618D02406683325D046D48400B6511AB1E840240D62DAADC086D4840299EF9E791840240EA27CCC6456D4840DE888115447F0240982095124A6D48403DFFF755F2790240E947B06B4E6D4840F81F804EBC7A0240BA1E6B21BA6D4840B92C63ADE57A0240A6A91936D56D4840EC0676723D7B0240BF155C77126E4840A57043FE737B0240CF70D142396E484014567A21C47B024044B97AAF6F6E4840057BB8CFAB7B02405B1D66538B6E48405EF1FC2EB685024028F6B7188E6E484054C2CEB7B6850240E597DD188E6E4840D10C1BF2FF87024029AC87B98E6E4840220C516F3B8C02408959AF41906E4840854BE93C5E8C024014A6AA32906E48405AC01BAF818C02406332F300906E4840A592ECA1A58C0240098E419E8F6E4840B02E736EC88C024092CAA6228F6E48407AC2C8D92F8E0240B074EDFE876E484018E0C6735E8E0240021B3012876E48400A91EFBEE68E02409716B25E846E484061C9EB68158F024035297970836E4840168900D401900240328887BE7E6E48404929BC23029002409A78F1BC7E6E4840D767AB3E3591024008A580AB786E48403947277F35910240BE6038AA786E484042F63B87359102401B7C12AA786E4840D1DC6EB449910240CB909546786E484023CFA0F90E990240CD3EDB44526E4840A2697299129A02400682EF394D6E484091AD34F6159A0240850F38294D6E484025995F422C9A02405AA6E5C14C6E484074E1B97FDB9B02405193D9E9446E484033E55CE4F69A024064F3173D376E48408049D0B3C89A0240A84BC379346E4840EAF07A3AAC9A02403CEFBAC5326E4840DF685E389B9A0240F5C444C1316E48403F6BB92ED59802409326867F166E4840227BB0C72497024024D744AAFA6D484088C414D4C495024024BCB59AE36D484099A89CBE14950240CB50581CD86D484021F45FB8A79102404AB74EF5A06D4840B990D304428F024023A02CF96E6D484042B76E0EBC8D0240906F15CC2F6D4840E5FBEA04868D0240FFF0C510276D48400C3388D1558D0240ABF512F6056D4840', null, null);
INSERT INTO public."Quartier" (id, nom_quartier, ville, geom, code_postal, description) VALUES (28, 'Roquette', 'Paris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null, null);
INSERT INTO public."Quartier" (id, nom_quartier, ville, geom, code_postal, description) VALUES (29, 'Jardin-des-Plantes', 'Paris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null, null);
INSERT INTO public."Quartier" (id, nom_quartier, ville, geom, code_postal, description) VALUES (30, 'Palais-Royal', 'Paris', '0106000020E6100000010000000103000000010000001E0000007A0E9EE139B70240001D2D735A6E48400CF2575C86B602401E05DAE8446E4840F3548C2E76B402405E5CFD934F6E4840B0113FEE75B40240366151954F6E484033A6D736A6AE0240000B14BF6D6E484064F768001DA802407F07A0768F6E4840390310BE9CA602406FC6A73E976E4840DFEC9CC3D2A702408C99E076B56E48407933F04BD6A802408FA374ABCE6E48400894DEECD6A802401C17FEBACE6E48401549574410AA02407725BFEAEB6E484016F1D79979AA0240F4D4EC12F46E48408FCFB30B5CAB02404EC814970A6F4840AABB83F75DAB02407ED5B4C40A6F48400952571DDCAF0240CD4BD6BEF96E4840DEAFDC72DCAF02405CB391BDF96E48400F732D13F0B2024078BF5D5BEE6E4840818DD5038ABA0240F0DF119ED16E4840786484ACBBBA0240DB8E4EE2D06E4840761E1B83BBBA02409BEBFEDED06E4840435C0746B2BA024019469422D06E4840654B16C4A5BA02403A59DBF7CE6E4840DB7F627AA5BA0240C7A35FF1CE6E48408551957999BA0240A38DC1E3CD6E4840F2BE29A3DDB80240C2BB7A0DA76E484009122AC211B80240ACE6598B8F6E4840A398F6C5D8B70240A259BD72886E4840A7D4FC5D74B7024023C91EED7B6E4840E14E833FA6B6024035008C2B5D6E48407A0E9EE139B70240001D2D735A6E4840', null, null);
INSERT INTO public."Quartier" (id, nom_quartier, ville, geom, code_postal, description) VALUES (31, 'Chaussée-d''Antin', 'Paris', '0106000020E61000000100000001030000000100000028000000B294327400AF024013F145FB3B7048400203591161B2024094CCC12B38704840DBF786688BB402405350A4FE3C704840E2704A75BDB6024002002FBE3A704840E8E78CEAE8B602401FDA4A9A367048407BD5FC8360B60240C7F4300E1F704840D7C7731FCBB50240825B889D1F704840F840507E21B5024053B8697303704840011D615A68B40240E085D687E46F4840E30CB62F0BB3024026270F91A96F4840363926925FB20240AE63BB7A8C6F484027BFC49D31B20240A25F70F48B6F48405D41A01157AC0240A98183F27B6F4840267204CA1AA802405DBE5C5D706F48400C16125583A30240E080CEC2636F4840CCB8494EC29F0240D4903D74596F48404C2CDA14C29F0240B5D9CE76596F4840C5A523B8B99F0240D65D19D5596F4840B2DCBFAC529B02407F9994D74D6F48403E77C609509B0240F1F366D04D6F48408D048846D69B0240609C7BBA6E6F4840F8A2217F349C0240E47CB1A2856F4840B2DAB5B8A99C0240A3B96C22A26F4840DF4BA822319D024051D5ED0DC36F4840F630ADB9639D0240C22BAE97C76F4840F9168D35A09D0240EFEF6C20D06F4840E8722E2CB59D0240B536A315D36F4840682D2108CE9D02403A1A9597D66F4840352CB897BF9D02404D49C114DA6F48407367874CB99D0240A84B059ADB6F48400BFBD6B9919D02405B20DC29E56F48402F08CDBBD39C02401FAC601613704840C1F294E5619D0240BEA27481147048400498E3B1649D0240131422CB127048405F8B42AA35A4024097344E9820704840E43E6EA3F3A50240DF9CE7022670484047FB2BD4D5A8024020FA80973870484081ACB9E796A90240661AAA643C704840EB2B892CBAAC024027A5ACB53E704840B294327400AF024013F145FB3B704840', null, null);
INSERT INTO public."Quartier" (id, nom_quartier, ville, geom, code_postal, description) VALUES (32, 'Rochechouart', 'Paris', '0106000020E6100000010000000103000000010000002F000000C718630134CC02406DD73CACEC704840CF370A6773CC0240CACB8E89C7704840E0A616DA82CC0240D616175EB8704840A98EAB72D3CB0240D4C1618699704840C6067AA6CECB02408DF1A3019970484043089F7A59CB0240B43FF9A88470484027E5AF9727CB0240DE13B63774704840736CFC35FCCA024073CC193251704840B4929E0CEDCA0240E2B17C614F704840ABDF6EEF0ECA0240C4228D8B35704840C8449F9C4CC1024071FBC7F0327048403840806C47C1024013544C833270484034281046BCBE024025AC18F9327048409E644961FFBC02401A14558F31704840754330C5D7B80240C9D3E979347048409EBC6167A9B702403201573E36704840E8E78CEAE8B602401FDA4A9A36704840E2704A75BDB6024002002FBE3A704840D965871809B8024055CED0A5847048406E39709A2BB90240155D0D59B070484009B8BDD285B702404387C5A1E57048404DC915897DB70240A05E0FAFE67048403D6E3B1779B702406F1C7E3FE7704840346569017FB702405F7CA831E7704840E9865A27A9B7024000161ACFE670484087AF74C3DCB702400D237256E6704840A6654679F1B7024024880526E670484002242BE68BBB024026EC226BF1704840EDB1313130BC02402941F96CF370484079F3DA9764BC02400D81DC10F47048407C179DBC50C00240D7C93155007148403A47A80568C10240E4A680DE03714840F178E8446DC10240459983EF03714840FA1976F390C10240A6D3CC5E047148408D82A5ECE5C5024083405EE111714840381BFBA5F3C50240FA3F280C127148402B35D326F6C50240AE04F9131271484094F72C1932C602404B02EACE12714840FB8CC1EC8DC60240970445ED137148400B3A8B5AA8C60240C40FB71F147148409327295CEEC6024032AD53A5147148407C94C52726C70240C169CE0F15714840527C5A3860CA0240884E42381B7148407E1E772571CB0240B34F0F411D7148400D61139DC9CB02409711DDE91D714840C15053E8F5CB0240B5F7771E16714840C718630134CC02406DD73CACEC704840', null, null);
INSERT INTO public."Quartier" (id, nom_quartier, ville, geom, code_postal, description) VALUES (33, 'Clignancourt', 'Paris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null, null);
INSERT INTO public."Quartier" (id, nom_quartier, ville, geom, code_postal, description) VALUES (34, 'La Chapelle', 'Paris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null, null);
INSERT INTO public."Quartier" (id, nom_quartier, ville, geom, code_postal, description) VALUES (35, 'Amérique', 'Paris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null, null);
INSERT INTO public."Quartier" (id, nom_quartier, ville, geom, code_postal, description) VALUES (36, 'Val-de-Grace', 'Paris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null, null);
INSERT INTO public."Quartier" (id, nom_quartier, ville, geom, code_postal, description) VALUES (37, 'Sorbonne', 'Paris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null, null);
INSERT INTO public."Quartier" (id, nom_quartier, ville, geom, code_postal, description) VALUES (38, 'Porte-Saint-Denis', 'Paris', '0106000020E6100000010000000103000000010000001900000099ECE7FABED702400CB82189856F4840E935399286D50240F4A50435466F4840F302923530D20240C3E93A26526F48400FDBD179C4CE0240BE861CC85D6F48401382217575C80240782BC93B736F48409C4FE80A80C802401AAB04EA9D6F4840E418A50F86C802408678F803E06F4840B79DED7635C90240BD4D57021A70484094F38E965CC9024021E90F6A21704840ABDF6EEF0ECA0240C4228D8B35704840B4929E0CEDCA0240E2B17C614F704840C9367F52ADCF0240F14C67C044704840D3D515BBCBD70240BC0F040E337048408E6256EA20D90240DEBF2FF130704840347ED05754D9024044AB53CD307048408CB0044D6DD90240448DEBBB30704840EC8532E75ADD0240CAF58A19267048408CA699F23FDD0240F05A4406237048401CB7D60D12DD0240FEC5E7E71D704840DA7B2E70F5DC02406A2499491A7048403C1F2610B8DC0240403D166413704840852A3A43B2DB02408DA9FCF8F56F4840BC6A50C1ACDA024041CE1437D96F484040702F37E4D80240FEA4B67EA66F484099ECE7FABED702400CB82189856F4840', null, null);
INSERT INTO public."Quartier" (id, nom_quartier, ville, geom, code_postal, description) VALUES (39, 'Bel-Air', 'Paris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null, null);
INSERT INTO public."Quartier" (id, nom_quartier, ville, geom, code_postal, description) VALUES (40, 'Saint-Lambert', 'Paris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null, null);
INSERT INTO public."Quartier" (id, nom_quartier, ville, geom, code_postal, description) VALUES (41, 'Ternes', 'Paris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null, null);
INSERT INTO public."Quartier" (id, nom_quartier, ville, geom, code_postal, description) VALUES (42, 'Saint-Germain-l''Auxerrois', 'Paris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null, null);
INSERT INTO public."Quartier" (id, nom_quartier, ville, geom, code_postal, description) VALUES (43, 'Saint-Germain-des-Prés', 'Paris', '0106000020E6100000010000000103000000010000003400000041E2BF0C18B202403301D9AC496D4840B732461D27B202403C3033E42B6D4840E97177B540B00240BCA9B152336D4840A97CCAF365AE02407838C4D22B6D48404AA14A01E9AC0240F9505335276D4840687FA44AE5AC024083889789286D4840930A126B5EAB0240E78111BE236D48404015AEB815AA0240826A5695236D48405F1CEB8EB7A80240635ADC16206D484087C8F4ACF8A702405E207EF31D6D484053279C2215A70240722A433D1B6D484012F7D34481A40240B28B3568146D4840ADE3832CE5A20240FC43A3D30E6D484087E57EE999A00240EC904B98086D4840D4C3BC3C6DA2024034485C74226D4840FCB56C2A91A20240050D856F266D4840313DF9E4B9A40240C8E5F1895D6D48409223071BC7A402406A8D0AF45E6D4840A73A565346A50240F761DC736F6D48405B014C0A90A502401AB51127776D4840CC1D1CB52FA602401E758D10856D484079C19E1A44A7024024D68CB49B6D4840077D7CDC78A90240451C4AD7C66D4840BA62DC4A7BAA0240439BC1AFDA6D48400B0331377EAA024061B139E9DA6D48401B63A35B87AA024052FFF79CDB6D48408774A357D9A80240B65A549BE36D4840108A53C5CCA902409312FD2AFF6D4840052CF92960B102404CE8150BE66D48400481F7C6CEB002402BA68250D56D4840EC63F47BB8B002406CBA75BFD26D4840D3F194754AB002407EC1DCD2C56D4840ABB9547F5AB00240E2A29298C56D4840FFD4055F71B00240972CC13FC56D48401BDEF97B85B00240F2A6C4E2C46D4840C9C7F69899B002404499F570C46D48405773D26CAAB002409A3127FFC36D4840CF83E334B9B00240B101A084C36D484044FDD1B4C7B0024072F8CFFDC26D4840A4170E8ADAB002401EE1B921C26D4840440B87FDE8B00240F716174BC16D48401CB98FB9F5B002409BE3CE72C06D4840AEBA566A00B1024003E2CA89BF6D4840488589FF0BB10240E7F7BB5DBE6D484029DADB8515B102402CF2BA29BD6D4840D1E569BD22B102405D6B4B87BA6D48400AB8AE7825B10240A8A59FACB96D484072E6112B2BB102408B6D50A6B76D48407F707B1198B10240A6A16DA7866D4840DD6F5614B6B102401C020A697A6D48407B2BEE9B1EB20240D8A1918F4D6D484041E2BF0C18B202403301D9AC496D4840', null, null);
INSERT INTO public."Quartier" (id, nom_quartier, ville, geom, code_postal, description) VALUES (44, 'Javel', 'Paris', '0106000020E6100000010000000103000000010000007A0000004096BF6134420240F412E88BDF6A4840589821C06D450240503A049ECC6A48402940CE0CA24502405FB70557CB6A4840A72E34FDB63F0240A285E0418A6A484060C14FB8723E0240111B08577C6A4840932B1990723E02408AAB51557C6A4840C68027E5563E024064080CF87C6A4840F721942DEC3D0240B7A4AB6B7F6A48401881D386C93B0240BEF191FA8B6A4840F90A7AAAA93B024032BCF1B58C6A4840072D9A08733B02404A883DF78D6A4840CEBC9F1CA136024092A891304F6A4840818E710E3D3402401B587F0C306A48402D0BA06ED232024004F47767226A4840879537980C300240FA95A0B1076A4840453173F1A32F02407D507BC1036A484047168172F62E0240BCAD233AFD694840401684DD942E02406EB1138EF969484005B607AB84240240FBCB9FA5F6694840CBDBF2F61B2402407F26F698FA69484080E715EA782302407DDDA7546E6A4840C7A5FAAA832302404A3C00AF6F6A4840C880FC7E9A2302404D74D5AC706A4840F05935140F24024088FB19BD756A4840E7BBC693192602405127EC2F886A4840C125A3D5192602403F163432886A48407D95171153270240778A06FB926A484012F636A3D1270240F8C99A56976A4840B4C555D516280240C8F37BB8996A48407673AB1240280240D645F3239B6A484085E9BB8FE6280240306B1CD59E6A48402EA4F0950B290240FBBF4BA79F6A4840647EA8660B290240ADC967A99F6A4840C962B6F9062902400F7614DC9F6A4840B1B9AF88A62802401D14702CA46A484086AFB8FB90240240F2AFB3F0D26A4840F4B61975862402400B62BF0CD36A4840DCE02838742402405708563DD36A484091430C79AE2302409BDE444CD56A4840A26E8C2A6E2302402706A0F7D56A484016F7CE1DD72202400BD1D751D46A484082183A76EE200240439553FDCE6A484032232F4E021F0240DF86F19EC96A484003147BE8FE1E0240B13E7595C96A4840788EFC35491E0240589B319AC76A4840F62A86C8121E0240760F4002C76A4840CC377C0FC61D0240E4FF0A2CC66A4840D6443F74C11D024030D42D1FC66A4840624D43EE681D0240CD050828C56A4840914A24AC8C1A02400D108F2BBD6A48400D5A98018A1A0240E4121D24BD6A48405137E148121C02408C7F603ED66A4840B0D82504141F02400070507F076B48404A9FDA760624024059367794546B4840BA7252E47C2502406C65E6C66D6B484046459328B8250240836BE8C7716B4840F3D74B17642702404DC4AA918E6B48403856EE98232802407E3744719B6B4840AE32D3E3802802406D78E8B7A16B4840C2B1710DE92A0240543C4A24C96B4840A965AB12E92A0240C596A324C96B4840AC7E1EDA2B2C0240775BB4A8DE6B48409F9663E42B2C02401D1D65A9DE6B484085BD783B8F2D0240038F51BDF46B48403E8CCF5A482F0240C56427A0106C4840E0061414D92F0240329C34C6196C484082824664E62F0240BD6E139B1A6C4840EDF5F6A101300240FB63A74E1C6C48406A10E2AB01300240ADA8484F1C6C4840E0E36874743102404AF4DB38346C48409A32AD6A9C330240E70CBFCD576C48408675CBA8FF330240E7CDD9325E6C484071EA21D2A9340240D93D502B696C48405B1C20E3A934024035CA352C696C48404433BC24463702400881CFD88C6C48401945F55E51390240BB25DCCAA66C48407595319EB63B02405273D332C56C484041CFA9B0B63B0240E29CAB33C56C484010B5ACA3853C024071C485AECE6C4840561ECFECCC3C02400C9F79F2D16C484073B64B79123D0240AC280C22D56C484041AD518E5B3D02408D9D0D7BD86C484014AD392D94400240AC7DF9A0B86C4840667628FE08460240C129E39D826C48407FC90E4C1749024060065224646C48406985EB3DB84B024035C1CA3C4B6C4840C4D1CD65CC4D0240678C722E3E6C48400E2609CFE9500240FACB88AF2A6C4840D81E08B993570240B60D46E7006C48408AD07F3E425C02403F309B95E36B484062FD44BDDA5B02401D3FFCF9DC6B4840A3F19A88D2580240446B08EBC46B48401BCE5479CD54024028698C8DA56B48406BB2ECFF0554024090044C4F9E6B4840890F6B70F9530240ED6365C69D6B4840C2854D01EF5302400E41B7479D6B4840A6D44F78E7530240D2A3BAE99C6B48402CEF4B29DF530240B97687819C6B4840A4472F46D65302407FA0D2009C6B4840614514E6CF530240DBA4CDA09B6B484054B2DF6EC6530240B23595079B6B4840B6034C7A9B52024000547BEA846B48401D50DF4DC9510240908453FB706B4840968AAC5CB950024008482DC8566B4840173B8578D94F0240EBA85E24536B484031CE8A79284D0240C84F6F03486B4840AFC9CA5A554902403E875CFA376B4840C3784C8A4E4702408FDA65692D6B48407D7568268246024059E926FC286B484021BB6FDBF2440240B944C1AF1E6B4840D71DE7281B4002400ED4655CFF6A48401A777751144002404F35F389FE6A484048B2EDBF12400240D73E2258FE6A4840F9B2B8AD0E40024040BE1AD8FD6A4840D6D58DF90D4002408C2AF6C1FD6A4840C5758D570A40024063462B51FD6A4840E12F62C807400240CA198C01FD6A4840D20CE129FB3F02404892A277FB6A484023CBA3E3F43F024044A4C3B5FA6A4840B6BDA1867B3F024050702AECEB6A48409B4BB0806C40024066DC1BC1E76A48404096BF6134420240F412E88BDF6A4840', null, null);
INSERT INTO public."Quartier" (id, nom_quartier, ville, geom, code_postal, description) VALUES (45, 'Monnaie', 'Paris', '0106000020E61000000100000001030000000100000032000000753A2927CFBE02402F3ACD4CFA6C484060A538E5CDBD024051460185D86C484025188190FEBA024065641BBEE96C4840CF76CFDFBEBA024084E2FE8CE96C4840D8D8C5A40CBA0240029A2EDCEF6C4840B83318AD5BB902402B7484CBF66C4840B7F2C21DCDB802405417EAFDFC6C484070ABDFEDC3B702405546D1B50C6D4840D29A415637B70240281497FA116D484033F16A0D9AB602400E9579DF176D484074A6E4EB26B602404602F62F1C6D4840B732461D27B202403C3033E42B6D484041E2BF0C18B202403301D9AC496D48407B2BEE9B1EB20240D8A1918F4D6D4840DD6F5614B6B102401C020A697A6D48407F707B1198B10240A6A16DA7866D484072E6112B2BB102408B6D50A6B76D48400AB8AE7825B10240A8A59FACB96D4840D1E569BD22B102405D6B4B87BA6D484029DADB8515B102402CF2BA29BD6D4840488589FF0BB10240E7F7BB5DBE6D4840AEBA566A00B1024003E2CA89BF6D48401CB98FB9F5B002409BE3CE72C06D4840440B87FDE8B00240F716174BC16D4840A4170E8ADAB002401EE1B921C26D484044FDD1B4C7B0024072F8CFFDC26D4840CF83E334B9B00240B101A084C36D48405773D26CAAB002409A3127FFC36D4840C9C7F69899B002404499F570C46D48401BDEF97B85B00240F2A6C4E2C46D4840FFD4055F71B00240972CC13FC56D4840ABB9547F5AB00240E2A29298C56D4840D3F194754AB002407EC1DCD2C56D4840EC63F47BB8B002406CBA75BFD26D48400481F7C6CEB002402BA68250D56D4840052CF92960B102404CE8150BE66D4840A39ED72F60B10240561D030BE66D484002E2D0B74BB3024064D493ADDF6D48406DC7149C11B9024022CE93BBAC6D4840CDBD45121AB902402E87FA70AC6D484091BC9E694CB9024097AF20B5AA6D4840B088096412BA0240ABBD75DAA36D48402EE875F549BC02404127FE23856D484035D53450D0BD024076AA4E11706D484077DE6C5310BE02405940D8406D6D48404FCD830B2CBE02408947A5236C6D484029EA41CEB9C102401255347C516D48406657EBBD17C1024057ECE0AA476D48403C45FBAD28C002401ECC8589276D4840753A2927CFBE02402F3ACD4CFA6C4840', null, null);
INSERT INTO public."Quartier" (id, nom_quartier, ville, geom, code_postal, description) VALUES (46, 'Bonne-Nouvelle', 'Paris', '0106000020E610000001000000010300000001000000160000000D723AECE8CF02404C4E3981A56E4840C36B8B67BECE02403939E315846E484041347815FEC5024007332EACA56E48409D0DA9C7FDC50240167C5AADA56E48409539C04B53C60240C363F75DB56E48408FC2FEE2A8C602401D614BC6CA6E4840AA27FBE6FBC60240D96A2187E46E48408120AFCC4FC702404EDD6AE9026F4840F8921CB363C7024089C379EC0B6F48405C4BBF0C68C70240C5A87E38156F48402847A1841CC8024056C744952C6F4840C086B4EA2EC8024082F50AE6346F4840F7FBBEB972C80240A9F0BF605A6F4840E3F955FB78C802406B253700656F48401382217575C80240782BC93B736F48400FDBD179C4CE0240BE861CC85D6F4840F302923530D20240C3E93A26526F4840E935399286D50240F4A50435466F4840317D87CBE4D20240895613E8FA6E48405C2C9E78FBD102409E11CCB4E06E484092CC97F4D7D002401867752CC06E48400D723AECE8CF02404C4E3981A56E4840', null, null);
INSERT INTO public."Quartier" (id, nom_quartier, ville, geom, code_postal, description) VALUES (47, 'Madeleine', 'Paris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null, null);
INSERT INTO public."Quartier" (id, nom_quartier, ville, geom, code_postal, description) VALUES (48, 'Saint-Ambroise', 'Paris', '0106000020E61000000100000001030000000100000035000000C2039A3AAFF70240C763A03BCC6D4840D6D6D52921F202405D71D9AFAF6D4840426846EFCDF102407AA36766C56D484086B3E898E2F00240020247B5026E48409E7AFAD991F0024078AF92C0176E48404F0983B928F00240A12B4C95336E48405C0CF8D3D2EF0240294ED5CF496E4840B21218D769EF02405605CCFD646E4840CDE91060F8F50240730313D78F6E4840A7D7379DF8F6024090737B73966E484059F095D98AF90240E3CA7C84A86E48400F2E8601E6FA0240E2540B4AAD6E48408E982F6C5EFF0240F1F9C80BBA6E4840E1083EED510003406D62A0D2BC6E4840EF143BAB2D060340ECBD4FC4D26E48408ECFE2B6EC070340A343CAA8D96E4840519E24CE3D0B034057F2D2B7E66E484082AFA5991F10034020AD572EFA6E4840D2305AA0A9100340416B9454FC6E4840FEEBF6D3C0100340B8EF637DF96E4840569B3FA6F6100340A48785E6F26E4840C0B33E3D0A1103404A8D8380F06E48405CE7900B521103400FD1EBB5E76E4840CC46794B861103406B8977D9E46E4840700C9E34D01103404E314BCDE06E48401BD696349C1203403F5E65A1D56E48408C6AE565F7130340AD85F39DC26E484099BD8FC24014034000FD179CBE6E48405235EB22421403406D43D788BE6E4840E19681A46214034001FB47C2BC6E484072564F8A8914034029D76CA2BA6E48407266B3C8F1140340014EE1F0B46E4840BAF29BECAF1603407217D1929C6E4840EEF3A474191903401EE24DD97A6E4840465EEAA12F190340FA9ACB9D796E4840C11DE1803F190340CFBA4556786E48407AED8F415F1903401EAD3CC1756E4840A2257DBD77190340A4866FC8736E4840FA08FB646E190340BC11D1A2736E4840C57191574E190340017DCF21736E4840151355AA0A190340657F6911726E48409FBAB4507A160340F54E3856666E4840A8501D940D1503403F7DF708606E4840555085F581120340CA23000B586E48407A777EE78B0F03402B715D20486E48409E9BA94B9E0C0340DEA1CA7B386E484079365C38150A0340C193E2CA2B6E48405540244BE7070340EF27D1E3206E48402B0DEB765D07034094F68DD01D6E4840DC957287CA0303400D7F29570C6E484019CC1647C1010340C6420F31026E4840E0296A4985FD024065C4DFF8EB6D4840C2039A3AAFF70240C763A03BCC6D4840', null, null);
INSERT INTO public."Quartier" (id, nom_quartier, ville, geom, code_postal, description) VALUES (49, 'Sainte-Marguerite', 'Paris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null, null);
INSERT INTO public."Quartier" (id, nom_quartier, ville, geom, code_postal, description) VALUES (50, 'Archives', 'Paris', '0106000020E610000001000000010300000001000000170000005D49E77EA5F20240CEE82EDD8B6D48401B733A9DB6F20240873527B2886D4840309950E264EA0240A995A2CF9E6D4840910BA87118EA024087BCE952A06D4840F3C92A2F14E70240C12BE107AF6D48403938E4DFDDE40240ECB6C269BA6D4840F3165A29B4E202408BAC09D6C86D48405F5B6D5CFEDF02400A43028BDD6D4840C53A51DDE1DF02403E63359FDE6D4840F674DEB3D6DE02402AD00EBFE96D4840BEF8FD1EE9DD02407FB78D6DF46D4840F6442622F0DA024056EB06A3166E4840D782323BA1DD0240EE53479E3C6E48403921A24CE5E00240156EB6BA696E4840EF934014CAE302406E1BFA4F4F6E4840F02632A792E80240759D6CA8446E48404F0983B928F00240A12B4C95336E48409E7AFAD991F0024078AF92C0176E484086B3E898E2F00240020247B5026E4840426846EFCDF102407AA36766C56D4840D6D6D52921F202405D71D9AFAF6D484011FA1C6221F20240FBA218A1AF6D48405D49E77EA5F20240CEE82EDD8B6D4840', null, null);
INSERT INTO public."Quartier" (id, nom_quartier, ville, geom, code_postal, description) VALUES (51, 'Porte-Dauphine', 'Paris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null, null);
INSERT INTO public."Quartier" (id, nom_quartier, ville, geom, code_postal, description) VALUES (52, 'Chaillot', 'Paris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null, null);
INSERT INTO public."Quartier" (id, nom_quartier, ville, geom, code_postal, description) VALUES (53, 'Quinze-Vingts', 'Paris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null, null);
INSERT INTO public."Quartier" (id, nom_quartier, ville, geom, code_postal, description) VALUES (54, 'Belleville', 'Paris', '0106000020E610000001000000010300000001000000460000009C96DD27D91003404F5F720DFD6E4840D2305AA0A9100340416B9454FC6E48407E49269994100340B5D191D8FE6E4840FFD606E08C100340F99719C5FF6E484038880CA65810034000224735026F484074B30AA3FB0F0340270EDD8C066F48403DAD87D2880B0340C55843B63B6F4840DE8B2185620B034029C38F963D6F4840DAD4A176DA08034013229A545D6F4840B8055311950803409878B9BA606F48405A5A51CBF606034029A41805756F4840C6CF3336E10603404D6DB313766F484092C92917B70603408CC7BF2C786F48401385BD6E1906034051E2D906806F4840B7B91AD06A04034091A0AA589A6F48407EB0B99C370403409051C8799D6F48406049BD582104034032D228D69E6F4840CB4EB3AB12040340112EC6BB9F6F48400A4CB23523080340D8FDD3D5B16F48406AA7E87BBB08034022217C67B46F4840052B86D34F09034053F56CD0B66F4840DE24993FF00903408A6CCB3DB96F4840C1127C9EA80A0340249C42FDBB6F48404C9AEF92720B0340B1F2D4C1BE6F48406CFC31B61F0E03407D7FC978C76F4840DCE2AA13D10E034010E34E9BC96F484038A6C837800F03401607A971CB6F4840AA018603971003403F715B4FCE6F4840E496528E58130340E6DA08EDD36F48407E3F2F335214034048ED541FD76F484001917B4A66140340A9480282D76F4840AACB24349E140340E3D7E2B0D86F48405316D0C8EA140340E2D97A9EDA6F4840872D82CAE61503405AF929A8E26F4840478849972417034070FD5DB8EC6F484031B0BDEC19180340263969C9F26F4840C25F12504118034087F94B87F36F4840CDE7A9386E180340FADA7019F46F48403684DE75931803400A959451F46F484099EBD8A17A1903404C468637F56F484048D5C3490E1B034090F4CAA7F66F48400B1CE316BF1B034063430208F96F484050B27EB29F1C0340C3B155E7FE6F4840F57135BDB61D03403F843BF8067048400F09B942431E03401C4C61FB0A704840E48AAD9E881F0340A117139B0E704840AA3485B3572203406D2A3F360F704840263AF2FA57220340BDFEEF350F7048403C39374BBE220340008D54C10E70484049ED4856C0220340A7CCFFBE0E704840FF652754F82403408910D83A0C704840FDB932EAF32503405E3CB6710B7048405B2528CCA229034040DDAD4F087048409817B5689E2A0340BFC1CF02EC6F484028BB440ECC2A0340F905D2D5D16F484041AD9920532B0340176D93C1B56F48400A14B1974F2A03406DEB8A558C6F48407A11BB8FB729034044A3EA5E736F4840EE38E6DB042A0340456784B4696F48400CC2F18D992A03408AE5304A5E6F4840CF48ED8592290340A5FB34DB5B6F4840262F5385762703407AD7EF535A6F4840E63BBE67172503408E9505D64B6F4840613475FDE2230340A47FE532456F4840F2187D856A220340CDB92FA6416F48408EB1178C522103406DEA20AB3D6F4840CAB66B97001E03408A6ABB90306F484002C0AFA3AC1803406339A4AF1B6F4840385FA9AACF170340B8CE823C186F48409C96DD27D91003404F5F720DFD6E4840', null, null);
INSERT INTO public."Quartier" (id, nom_quartier, ville, geom, code_postal, description) VALUES (55, 'Saint-Vincent-de-Paul', 'Paris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null, null);
INSERT INTO public."Quartier" (id, nom_quartier, ville, geom, code_postal, description) VALUES (56, 'Picpus', 'Paris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null, null);
INSERT INTO public."Quartier" (id, nom_quartier, ville, geom, code_postal, description) VALUES (57, 'Gare', 'Paris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null, null);
INSERT INTO public."Quartier" (id, nom_quartier, ville, geom, code_postal, description) VALUES (58, 'Croulebarbe', 'Paris', '0106000020E610000001000000010300000001000000390000001481587B34D00240E456829B1B6B48400046E26668D002406FB7F0741B6B484095D371036AD00240775AA2761B6B48404AD7803A78D00240B866CC8D1B6B4840A21DEE2D86D0024043289EB21B6B4840A4B712BF93D00240F0FBBFE41B6B4840F63FF6D4A0D00240DC5AC9231C6B4840438D35ED01D202402A7008C2FB6A484028823EF447D30240BD2C035BDE6A4840EED480E5A4D30240AB090016D66A4840B5E5D9761FD70240A4F51A7A866A4840A84E4C70C7D70240FEF2CCBD766A4840DE46BE8C38D80240246A25256C6A4840915024783ED70240664DC329676A4840A09DA95CA4D602404ACDD317646A484082130A0904D602403FD12AE6606A4840678C4E5A95D50240B372AEB15E6A4840F99AC40993D4024023773E8C596A4840877C94DD44D4024089CA8AFD576A4840750EA7D905D40240931627BC566A4840063BB8D0C1D302404FDC2861556A484044D9B639B9D202404C06AC1B506A48406B7AD95266D20240F9B3D6744E6A484042CCA9AA2CD202403B0C94654D6A4840BAA8396725D20240EDFD69434D6A4840526000D976D10240B29382A8496A4840C45BD548DFCE02401B1368853C6A4840EEE96339A0CC02408FAEEC1C316A4840943A582FDACB02403404B2DA2E6A48403118FDCFFCCA0240ACE9A43A306A4840233A97DDA9C9024077760D07366A4840299FC62386C90240B1537BA3366A4840564AE5437EC902406559E6C5366A48406AB4CB1B5BC90240B356925F376A4840426C3D9FB2C60240071DECFD426A4840E49B9C0792C40240A44659614C6A4840C82BF99841C40240A1C77EC34D6A484065C1D0DE1BC10240B3DEA8975B6A4840DD6F9F26B9C00240CC963D4B5D6A484089747A182BBE02400A42009B686A484002C75F63FDBD02408C363065696A484071E71CA1E5BA02407D25301E776A48405CAC4E81ACBA02407794A21B786A4840B20032E0AABA02402A4B7F52846A4840AC0BDFE1A8BA0240228DFB628A6A484004B5DCADA8BA02401BF9401C8F6A48401E7FA90CEABA0240B560D09AA36A4840DD5CD40891BB02403197BF2EDB6A48400C809E0C32BC0240C37DEF2B1D6B4840D9B23BD532BC02402ABE92971D6B48408210AFD68FBC02406E782C844F6B4840BA42C73A78C1024043BE47FA3E6B4840F053BE61A9C602404680E8572D6B48406EF95CAACFC60240AC1B19FB2C6B48407ABC4F160FC70240067556662C6B4840E98F25FDB8CA02405248DBA9256B48401481587B34D00240E456829B1B6B4840', null, null);
INSERT INTO public."Quartier" (id, nom_quartier, ville, geom, code_postal, description) VALUES (59, 'Plaine de Monceaux', 'Paris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null, null);
INSERT INTO public."Quartier" (id, nom_quartier, ville, geom, code_postal, description) VALUES (60, 'Grenelle', 'Paris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null, null);
INSERT INTO public."Quartier" (id, nom_quartier, ville, geom, code_postal, description) VALUES (61, 'Petit-Montrouge', 'Paris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null, null);
INSERT INTO public."Quartier" (id, nom_quartier, ville, geom, code_postal, description) VALUES (62, 'Vivienne', 'Paris', '0106000020E61000000100000001030000000100000018000000AE93B550D8BA0240FA0C849CD26E4840786484ACBBBA0240DB8E4EE2D06E4840818DD5038ABA0240F0DF119ED16E48400F732D13F0B2024078BF5D5BEE6E4840DEAFDC72DCAF02405CB391BDF96E48400952571DDCAF0240CD4BD6BEF96E4840DEB3C6A6C8B0024058E0BCAF196F4840E360AAF6D4B0024099FE0B581B6F4840951D79048AB10240C65B4323366F48403513BF59B9B10240E97445F84C6F484027BFC49D31B20240A25F70F48B6F4840363926925FB20240AE63BB7A8C6F4840908CD546C7B702403063B6549B6F4840452EE07D1CB80240CA77D63E9C6F484086EC391C69B802404D1D56119D6F48403494E5EC9ABE0240BEDC8BF28C6F484003981BF39FBD024027EBCE1D606F48406D14BE643CBC024055FAEA232F6F48406C766D872EBC0240B92A71CB296F4840EC99E6CF6FBB0240EE8B22270D6F48401D59056869BA024099002F94E56E484026CDA898F5BA02402523CD5EE26E48401F843FDF5EBB0240C37FA2D9DA6E4840AE93B550D8BA0240FA0C849CD26E4840', null, null);
INSERT INTO public."Quartier" (id, nom_quartier, ville, geom, code_postal, description) VALUES (63, 'Arsenal', 'Paris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null, null);
INSERT INTO public."Quartier" (id, nom_quartier, ville, geom, code_postal, description) VALUES (64, 'Montparnasse', 'Paris', '0106000020E6100000010000000103000000010000003C000000DD5CD40891BB02403197BF2EDB6A48401E7FA90CEABA0240B560D09AA36A484004B5DCADA8BA02401BF9401C8F6A4840AC0BDFE1A8BA0240228DFB628A6A4840B20032E0AABA02402A4B7F52846A48405CAC4E81ACBA02407794A21B786A48404DBFA2DB2ABA0240B1CD30577A6A484053EC78D21FB80240F7120059836A48403EDA5CC61FB8024063153559836A4840BE89E1E155B70240C0DF5DCA866A4840F867A1C48EB30240BAA6E854976A484036F7CEA17DB30240CA075AA0976A4840C7C58DCB4BB10240197A8D35A16A4840B391075F09B1024085C10858A26A484066D4670609B10240BDD28C59A26A484011EA27B482B00240610030A6A46A48402555313C4FB002407BBEE39BA56A484070A9AA570EB002402B972AE0A66A4840A2F904320EB0024001D6F7E0A66A4840E644B757D0AF0240A29494F4A76A4840A11D4011D0AF024083DCD0F5A76A4840269E866E9BAF02404E3871E0A86A484066F3DE3A1DAB024046E11396BC6A4840D183EC041DAB0240637E0097BC6A4840221B9AE975AA02403851D274BF6A4840A4069B935DAA0240E7C6B7DEBF6A484004C25176ABA80240A55E8F41C76A484080522CA5FAA6024043ACB8BEA76A4840B6015A7012A202405A797CB3C46A48409AE123BF669D02402B1E4B82E06A48405A8304DD1A990240FA0D380CFA6A48406FE9822C8D970240FF85512C036B48403C0CBB766B960240F86ABF88096B4840C0779BD8EC950240D5DF5BD71C6B4840775C8A8E20950240631F546B3C6B48402252215CC69402402E483E284A6B484020BD856C58930240AFC3F6E3816B4840BC8CEC7A3E9302403BA174D7856B4840045D4C1E1F930240757E369E8A6B4840AFC6ECBB69920240DC7BBE31A66B48408A1C2615A4920240A78FB33DA96B4840F2248E9A9F940240C19B7D10C36B48400D63228C029902408874C495FB6B48408AFC0C716D9C0240D4856169E96B484044626203ED9D02400D3F169EE16B4840E9CB380EED9D02406359DB9DE16B48404AB6B9D27AA302402480EE3CC46B4840FD3ABA57C3A40240F7808773BD6B4840EDE0D8DB0BA602402E155CA9B66B4840D1202DB8A2AB0240AD085E15996B4840DAC11DC4A2AB0240EE7F1B15996B4840FF5844E19EB10240D6D176C4796B484005A43C0C9FB10240CCBA96C3796B484097254ED7AFB70240FECDA0F05F6B4840257778E9AFB70240FD3853F05F6B484049D9118FC9B702401A00758D5F6B48408210AFD68FBC02406E782C844F6B4840D9B23BD532BC02402ABE92971D6B48400C809E0C32BC0240C37DEF2B1D6B4840DD5CD40891BB02403197BF2EDB6A4840', null, null);
INSERT INTO public."Quartier" (id, nom_quartier, ville, geom, code_postal, description) VALUES (65, 'Muette', 'Paris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null, null);
INSERT INTO public."Quartier" (id, nom_quartier, ville, geom, code_postal, description) VALUES (66, 'Batignolles', 'Paris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null, null);
INSERT INTO public."Quartier" (id, nom_quartier, ville, geom, code_postal, description) VALUES (67, 'Epinettes', 'Paris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null, null);
INSERT INTO public."Quartier" (id, nom_quartier, ville, geom, code_postal, description) VALUES (68, 'Bercy', 'Paris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null, null);
INSERT INTO public."Quartier" (id, nom_quartier, ville, geom, code_postal, description) VALUES (69, 'Parc-de-Montsouris', 'Paris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null, null);
INSERT INTO public."Quartier" (id, nom_quartier, ville, geom, code_postal, description) VALUES (70, 'Saint-Fargeau', 'Paris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null, null);
INSERT INTO public."Quartier" (id, nom_quartier, ville, geom, code_postal, description) VALUES (71, 'Charonne', 'Paris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null, null);
INSERT INTO public."Quartier" (id, nom_quartier, ville, geom, code_postal, description) VALUES (72, 'Gaillon', 'Paris', '0106000020E61000000100000001030000000100000011000000DEB3C6A6C8B0024058E0BCAF196F48400952571DDCAF0240CD4BD6BEF96E4840AABB83F75DAB02407ED5B4C40A6F4840BB27ABF75FA70240F1C3B924196F4840AC5E4AC45FA70240A7837025196F4840727C945B2FA50240944ADFEB206F484026009CE377A402402B31B44B266F4840F550A1FFC4A302400A4AEA782B6F4840CCB8494EC29F0240D4903D74596F48400C16125583A30240E080CEC2636F4840267204CA1AA802405DBE5C5D706F48405D41A01157AC0240A98183F27B6F484027BFC49D31B20240A25F70F48B6F48403513BF59B9B10240E97445F84C6F4840951D79048AB10240C65B4323366F4840E360AAF6D4B0024099FE0B581B6F4840DEB3C6A6C8B0024058E0BCAF196F4840', null, null);
INSERT INTO public."Quartier" (id, nom_quartier, ville, geom, code_postal, description) VALUES (76, 'Maison-Blanche', 'Paris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null, null);
INSERT INTO public."Quartier" (id, nom_quartier, ville, geom, code_postal, description) VALUES (73, 'Champs-Elysées', 'Paris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null, null);
INSERT INTO public."Quartier" (id, nom_quartier, ville, geom, code_postal, description) VALUES (74, 'Hôpital-Saint-Louis', 'Paris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null, null);
INSERT INTO public."Quartier" (id, nom_quartier, ville, geom, code_postal, description) VALUES (75, 'Salpêtrière', 'Paris', '0106000020E61000000100000001030000000100000064000000DACF0315D5D90240278D2B7A5F6A4840ED277E0964D9024091987D185D6A48409D4BA35BE9D802403AB9AE42636A4840DE46BE8C38D80240246A25256C6A4840A84E4C70C7D70240FEF2CCBD766A4840B5E5D9761FD70240A4F51A7A866A4840EED480E5A4D30240AB090016D66A484028823EF447D30240BD2C035BDE6A4840438D35ED01D202402A7008C2FB6A4840F63FF6D4A0D00240DC5AC9231C6B4840934177565FD2024039D809DE246B48405C964541C2D70240425C626D406B4840E0FB858DCDD80240E447F2C4456B48401A70842A0ADB024063E4FC2F516B4840832ECC45C4DC0240E056F5F8596B4840E57EC20E8ADF024005676A13686B48401E6143AF29E40240F28BB8987F6B4840570EB87F93E40240F08CFDB2816B4840A5C442CDFFE4024036F3EAD9836B4840A314159D99E502407AD0A8B4906B48408C188CBC7FEA0240DE0D5F51FA6B48405A4AFF329FEA0240E451BAF7FC6B4840A0398FC2C4EA0240C01D6821006C4840778BC1BFE6EA0240DFE215FE026C4840071A6CF50BEB024072892E20066C4840D607725F16EB02405684B000076C4840147D138F2DEB024037DCD5BB096C484077CF721A34EB0240AA8279E30A6C48402AC37DE036EB0240AAC07A550B6C4840241C68773CEB024093EA2A3B0C6C4840CE87AA6F48EB024065923AC20D6C484061C543A34EEB0240AF1F3C7A0E6C48403C0A46A05AEB0240FE5461A60F6C4840E616CA9B68EB02403BF1CBC8106C48401C4A3E3E6DEB0240A22C091A116C4840A81DE04696EB0240347BBB8A136C4840A16F6C929CEB0240AA6177DE136C4840F6C98364B7EB024098F1E826156C484017BA792AD4EB0240348E6E5C166C4840153A71B5A0ED02400DAAF6AC266C484065B0CBB29CF2024067EC417EEA6B48402218819B15F40240CAED0AB7D86B4840752F1058F0F60240E08CC431B76B4840825B0A4A1AFA0240A4B3BB09926B484075157833B4FA024000DD25BA8A6B48408B141C06F9FA0240025D6874876B4840151FC94B16FC0240E3993B747A6B48405276172E21FD024022C1113C6B6B4840DDE534DD9AFF0240A97B6817476B484004F9191108FD0240522D61A5306B484027DC0EF6F1FC0240053F27EA2F6B4840B4FD6FD6E6FC02400B6EBD9B2F6B4840EF6C119DC7FC0240659AC3AC2E6B4840C1DE12E5DEFA0240B20A4B581E6B4840E249532DEEF902407E55E17E166B4840D5052991A8F90240B1AC7D39146B4840226100ED4CF902408A6EE63B116B4840B7DD170FC2F8024096112A790C6B4840C26EC9FD8FF80240BA05C3C10A6B4840C889A0355CF80240A0804EFB086B484047877477E9F70240B2E7130C056B48406EFC52EF72F70240882E079A016B4840E2B014C949F702406D169078006B484063867D7CF3F60240E3335E25FE6A4840C623A5ECCFF60240B6601B30FD6A4840384F78E8A0F50240AA593606F56A4840429D271778F50240D122AFECF36A484052533C4A62F50240E1A55356F36A4840B781C8AB41F50240C10A5B75F26A484062BBABC110F502403494FA23F16A4840ED3FCBA57AF4024001153AFAED6A4840AD11AB605EF402407E0CB961ED6A484052E89731B0F30240EC7715B6E96A48405E9FB8C993F302406EB5D71CE96A48400F74DEC523F0024092F65A91D66A4840323CB143D7EF0240E5039AF8D46A484053E2BA3B88EB0240C3AD270DBE6A484080C439923DEB0240DD1EDD7CBC6A48404109F915B7E80240E181C1EDAE6A4840883C3F8B3AE8024064315759AC6A4840298AF095E6E5024040B1A216A06A4840C32A8FA88EE50240FEE606199E6A4840CFEE535F65E5024068096F3E9D6A484082A64C1FF0E40240A914A4D19A6A48401D4148DF48E30240FF84B210926A48409AA91ACFE3E00240C0F90948856A4840192B1BC03DDF0240E7C7B4867C6A4840D7DA49442DDF02404A3426317C6A4840BB075CC9F4DE0240B481FA0B7B6A4840F46C25AEB7DE02407591D1CE796A4840B17F605F22DE0240121D1BAB766A4840EA6697F605DE0240189B2D12766A4840C612484675DD0240C8535407736A48402656E18E34DD02401B70F5AA716A48400A351C46FFDC0240E2AF998B706A4840E40DFF1208DC0240905875566B6A4840193C0C45F5DB0240762B0BF16A6A4840D15A305373DB024000E53E34686A48404F1A187105DB0240C6F4A4E3656A4840DACF0315D5D90240278D2B7A5F6A4840', null, null);
INSERT INTO public."Quartier" (id, nom_quartier, ville, geom, code_postal, description) VALUES (77, 'Odéon', 'Paris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null, null);
INSERT INTO public."Quartier" (id, nom_quartier, ville, geom, code_postal, description) VALUES (78, 'Porte-Saint-Martin', 'Paris', '0106000020E6100000010000000103000000010000003600000009277D694DE9024073E91A960B6F484053A66FB41EE90240C8499B620A6F48402C8CDB9FFEE80240F2B03B940B6F4840767D367CFBE802403ACE73B20B6F4840937317D7F5E8024067BC08E80B6F4840E6F2C438F5E80240F414EFED0B6F4840C1673555D9E80240CDEFAEF70C6F48409CCC642DA0E80240C68DCC170F6F48407B98890349E802409486AA56126F4840F9B19A5C1FE802405802DCE2136F4840B2C1E56CF7E702400481A979156F48402DC094B1D0E7024041F1CDD2156F484072CD8E759FE70240B2012344166F48409790948F52E70240E840BEF5166F484023AE947716E70240A03EF37F176F4840B9DDA29E9FE60240E8157991186F4840C0C72FD815E60240AEAB44C1196F484028CB391996E00240B41A6979266F4840EF21F5B741DF024015F8CD8B296F48404D27D8D6B3DB0240C55B09F3316F4840DC87EBD1DBD902404AB83190366F484033E3699D56D902407618C434386F4840587C788AC8D80240C52D5B3E3A6F4840E935399286D50240F4A50435466F484099ECE7FABED702400CB82189856F484040702F37E4D80240FEA4B67EA66F4840BC6A50C1ACDA024041CE1437D96F4840852A3A43B2DB02408DA9FCF8F56F48403C1F2610B8DC0240403D166413704840DA7B2E70F5DC02406A2499491A7048401CB7D60D12DD0240FEC5E7E71D7048408CA699F23FDD0240F05A440623704840EC8532E75ADD0240CAF58A192670484012C17EB659E002404B9090291E70484056C0B7DDACDF02407C5A874B087048408D9CCF369DE30240ACEE8414F36F4840E07F79D43EE70240001FDC30E66F4840E0A1C7606DE7024045C4F188E56F48407930345070E70240D81B677DE56F4840746DBD9044E8024054E2553AE26F4840EAA3146D50E80240364BAC0BE26F4840AA0F6E3576E80240B6F6556FE16F48406E757C9C4EEB024034C52CA9D56F4840AAE58141F4EE02404AE5AE4EB36F4840DCAAAF42B6EE0240B9F6B871B06F484084B26BCE67F1024086F0975C976F48409FF882E964F4024087F7347F4A6F4840D9C2FB5550F2024090081BD53D6F48406BBA51B467F102409F3B9CDE386F4840749703FCDFF002402B835FF9356F48409998B5896BF002405BE08D9A336F484037F36FB546EE02402673C0E2296F4840EDADCB2C97EC0240E335793B216F484009277D694DE9024073E91A960B6F4840', null, null);
INSERT INTO public."Quartier" (id, nom_quartier, ville, geom, code_postal, description) VALUES (79, 'Goutte-d''Or', 'Paris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null, null);
INSERT INTO public."Quartier" (id, nom_quartier, ville, geom, code_postal, description) VALUES (80, 'Ecole-Militaire', 'Paris', '0106000020E61000000100000001030000000100000026000000BC767407888F02409C90A02A916C48409FACDD1B0A8E02404A61B62F866C4840B6843152798D02405E5C808F826C484077F49B3E5A8B024045E68F93756C484003BF692E5A8B024039EC2B93756C48404870F5A6568802404416AFBF646C4840852E82215E870240550B5F745F6C4840EAA6D6CE84820240FE1FBE8A476C4840C11D235C568202409B97DE66496C48408D329A94297F0240758327F2696C4840EE4F9611807E02401425A2BB706C4840CEC141B1357E024068CFFDB5736C48408D03F1B3EC7D02405E3F2A95766C48407AF63252767D02400DD3863D7B6C4840757C9C073F7D0240813D417B7D6C484023939907F57B02403F8A6DDB8A6C484073238E97A77B0240CBB281B2896C4840B33BF2FC3B770240680868BF786C48402D4DB4B3DE76024078477259776C48402B8377856E750240BB3E27106F6C48405148D168FE7202407FCF44FC876C4840839E7068126E02409C913C6DBA6C4840D113DA28606802400F5CD91BF56C48405A167DC360680240898A4F22F56C4840BC7D33BE5E6E0240E8CD9547366D484024403F90AA7102403423C7995A6D4840CCD5E5BAA2730240CEA24517546D48403DFFF755F2790240E947B06B4E6D4840DE888115447F0240982095124A6D4840299EF9E791840240EA27CCC6456D48400B6511AB1E840240D62DAADC086D4840A2BC27CE618D02406683325D046D4840403601727B8E0240C5313400DE6C48407888E5DD77900240A4A0C906996C4840944CA7E177900240024B5006996C48407552DA50B98F02407B3000D8926C4840D423F543B98F0240E6ED90D7926C4840BC767407888F02409C90A02A916C4840', null, null);

-- Mettre à jour la séquence pour éviter les conflits d'ID
SELECT setval('"Quartier_id_seq"', (SELECT MAX(id) FROM "Quartier"));
