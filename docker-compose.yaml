services:
  backend:
    build: ./backend
    container_name: nextdoorbuddy-backend
    ports:
      - "3000:3000"              
    volumes:
      - ./backend/src:/app/src
      - ./backend/dist:/app/dist
      - uploads_data:/app/uploads
    command: npm run dev
    environment:
      - NODE_ENV=production
      - DB_HOST=db
      - DB_PORT=5432
      - DB_USER=user
      - DB_PASSWORD=rootpass
      - DB_NAME=nextdoorbuddy
    depends_on:
      - db
    networks:
      - nextdoorbuddy-network

  frontend:
    build: ./frontend/nextdoorbuddy
    container_name: nextdoorbuddy-frontend
    environment:
      - NODE_ENV=production
    depends_on:
      - backend
    networks:
      - nextdoorbuddy-network

  db:
    image: postgis/postgis:15-3.3
    container_name: nextdoorbuddy-db
    restart: always
    environment:
      POSTGRES_PASSWORD: rootpass
      POSTGRES_USER: user
      POSTGRES_DB: nextdoorbuddy
    volumes:
      - db_data:/var/lib/postgresql/data
      - ./docker/init:/docker-entrypoint-initdb.d
    networks:
      - nextdoorbuddy-network

  nginx-proxy:
    image: nginx:latest
    container_name: nginx-proxy
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx-dev.conf:/etc/nginx/nginx.conf:ro
    depends_on:
      - frontend
      - backend
    networks:
      - nextdoorbuddy-network


volumes:
  db_data:
  uploads_data:

networks:
  nextdoorbuddy-network:
    driver: bridge
